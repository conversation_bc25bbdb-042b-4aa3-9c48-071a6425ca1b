/**
 * 场景管理节点集合
 * 提供场景操作、场景切换等功能的节点
 * 批次1.2：场景管理节点开发
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Scene, Object3D, Vector3, Box3, Quaternion } from 'three';

/**
 * 场景状态枚举
 */
export enum SceneState {
  LOADING = 'loading',
  LOADED = 'loaded',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  UNLOADING = 'unloading',
  ERROR = 'error'
}

/**
 * 场景元数据接口
 */
export interface SceneMetadata {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  createdAt: Date;
  modifiedAt: Date;
  tags: string[];
  thumbnail?: string;
  size: number;
  objectCount: number;
}

/**
 * 场景配置接口
 */
export interface SceneConfig {
  autoSave: boolean;
  autoSaveInterval: number;
  maxBackups: number;
  compression: boolean;
  validation: boolean;
  optimization: boolean;
}

/**
 * 场景管理器
 */
class SceneManager {
  private scenes: Map<string, Scene> = new Map();
  private sceneMetadata: Map<string, SceneMetadata> = new Map();
  private activeScene: string | null = null;
  private sceneStates: Map<string, SceneState> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * 加载场景
   */
  async loadScene(sceneId: string, sceneData?: any): Promise<Scene> {
    try {
      this.setSceneState(sceneId, SceneState.LOADING);
      this.emit('sceneLoadStart', { sceneId });

      let scene: Scene;

      if (sceneData) {
        // 从数据创建场景
        scene = this.createSceneFromData(sceneData);
      } else {
        // 创建空场景
        scene = new Scene();
        scene.name = sceneId;
      }

      this.scenes.set(sceneId, scene);
      this.setSceneState(sceneId, SceneState.LOADED);

      // 创建元数据
      const metadata: SceneMetadata = {
        id: sceneId,
        name: scene.name || sceneId,
        description: '',
        version: '1.0.0',
        author: 'System',
        createdAt: new Date(),
        modifiedAt: new Date(),
        tags: [],
        size: this.calculateSceneSize(scene),
        objectCount: this.countSceneObjects(scene)
      };

      this.sceneMetadata.set(sceneId, metadata);
      this.emit('sceneLoaded', { sceneId, scene, metadata });

      Debug.log('SceneManager', `场景加载成功: ${sceneId}`);
      return scene;

    } catch (error) {
      this.setSceneState(sceneId, SceneState.ERROR);
      this.emit('sceneLoadError', { sceneId, error });
      Debug.error('SceneManager', `场景加载失败: ${sceneId}`, error);
      throw error;
    }
  }

  /**
   * 保存场景
   */
  async saveScene(sceneId: string, options?: { backup?: boolean; compress?: boolean }): Promise<any> {
    try {
      const scene = this.scenes.get(sceneId);
      if (!scene) {
        throw new Error('场景不存在');
      }

      this.emit('sceneSaveStart', { sceneId });

      // 序列化场景数据
      const sceneData = this.serializeScene(scene);

      // 更新元数据
      const metadata = this.sceneMetadata.get(sceneId);
      if (metadata) {
        metadata.modifiedAt = new Date();
        metadata.size = this.calculateSceneSize(scene);
        metadata.objectCount = this.countSceneObjects(scene);
      }

      // 创建备份
      if (options?.backup) {
        await this.createSceneBackup(sceneId, sceneData);
      }

      this.emit('sceneSaved', { sceneId, sceneData, metadata });

      Debug.log('SceneManager', `场景保存成功: ${sceneId}`);
      return sceneData;

    } catch (error) {
      this.emit('sceneSaveError', { sceneId, error });
      Debug.error('SceneManager', `场景保存失败: ${sceneId}`, error);
      throw error;
    }
  }

  /**
   * 创建场景
   */
  createScene(sceneId: string, config?: Partial<SceneConfig>): Scene {
    if (this.scenes.has(sceneId)) {
      throw new Error('场景已存在');
    }

    const scene = new Scene();
    scene.name = sceneId;

    this.scenes.set(sceneId, scene);
    this.setSceneState(sceneId, SceneState.LOADED);

    // 创建元数据
    const metadata: SceneMetadata = {
      id: sceneId,
      name: sceneId,
      description: '',
      version: '1.0.0',
      author: 'System',
      createdAt: new Date(),
      modifiedAt: new Date(),
      tags: [],
      size: 0,
      objectCount: 0
    };

    this.sceneMetadata.set(sceneId, metadata);
    this.emit('sceneCreated', { sceneId, scene, metadata });

    Debug.log('SceneManager', `场景创建成功: ${sceneId}`);
    return scene;
  }

  /**
   * 销毁场景
   */
  destroyScene(sceneId: string): boolean {
    try {
      const scene = this.scenes.get(sceneId);
      if (!scene) {
        return false;
      }

      this.setSceneState(sceneId, SceneState.UNLOADING);
      this.emit('sceneDestroyStart', { sceneId });

      // 清理场景资源
      this.cleanupScene(scene);

      // 如果是活动场景，清除活动状态
      if (this.activeScene === sceneId) {
        this.activeScene = null;
      }

      this.scenes.delete(sceneId);
      this.sceneMetadata.delete(sceneId);
      this.sceneStates.delete(sceneId);

      this.emit('sceneDestroyed', { sceneId });

      Debug.log('SceneManager', `场景销毁成功: ${sceneId}`);
      return true;

    } catch (error) {
      Debug.error('SceneManager', `场景销毁失败: ${sceneId}`, error);
      return false;
    }
  }

  /**
   * 获取场景
   */
  getScene(sceneId: string): Scene | undefined {
    return this.scenes.get(sceneId);
  }

  /**
   * 获取场景元数据
   */
  getSceneMetadata(sceneId: string): SceneMetadata | undefined {
    return this.sceneMetadata.get(sceneId);
  }

  /**
   * 获取场景状态
   */
  getSceneState(sceneId: string): SceneState {
    return this.sceneStates.get(sceneId) || SceneState.ERROR;
  }

  /**
   * 设置活动场景
   */
  setActiveScene(sceneId: string): boolean {
    const scene = this.scenes.get(sceneId);
    if (!scene) {
      return false;
    }

    const previousScene = this.activeScene;
    this.activeScene = sceneId;

    // 更新场景状态
    if (previousScene) {
      this.setSceneState(previousScene, SceneState.INACTIVE);
    }
    this.setSceneState(sceneId, SceneState.ACTIVE);

    this.emit('activeSceneChanged', { previousScene, currentScene: sceneId });

    Debug.log('SceneManager', `活动场景切换: ${previousScene} -> ${sceneId}`);
    return true;
  }

  /**
   * 获取活动场景
   */
  getActiveScene(): Scene | null {
    return this.activeScene ? this.scenes.get(this.activeScene) || null : null;
  }

  /**
   * 获取所有场景ID
   */
  getAllSceneIds(): string[] {
    return Array.from(this.scenes.keys());
  }

  // 私有方法
  private setSceneState(sceneId: string, state: SceneState): void {
    this.sceneStates.set(sceneId, state);
    this.emit('sceneStateChanged', { sceneId, state });
  }

  private createSceneFromData(sceneData: any): Scene {
    // 简化实现，实际应该根据数据格式解析
    const scene = new Scene();
    scene.name = sceneData.name || 'Untitled';
    return scene;
  }

  private serializeScene(scene: Scene): any {
    // 简化实现，实际应该序列化所有场景对象
    return {
      name: scene.name,
      objects: [],
      metadata: {
        timestamp: Date.now()
      }
    };
  }

  private calculateSceneSize(scene: Scene): number {
    // 简化实现，计算场景大小
    return JSON.stringify(this.serializeScene(scene)).length;
  }

  private countSceneObjects(scene: Scene): number {
    let count = 0;
    scene.traverse(() => count++);
    return count;
  }

  private cleanupScene(scene: Scene): void {
    scene.traverse((object) => {
      if (object.geometry) {
        object.geometry.dispose();
      }
      if (object.material) {
        if (Array.isArray(object.material)) {
          object.material.forEach(material => material.dispose());
        } else {
          object.material.dispose();
        }
      }
    });
  }

  private async createSceneBackup(sceneId: string, sceneData: any): Promise<void> {
    // 简化实现，实际应该保存到文件系统或数据库
    Debug.log('SceneManager', `场景备份创建: ${sceneId}`);
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('SceneManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }
}

// 全局场景管理器实例
const globalSceneManager = new SceneManager();

/**
 * 加载场景节点
 */
export class LoadSceneNode extends VisualScriptNode {
  public static readonly TYPE = 'LoadScene';
  public static readonly NAME = '加载场景';
  public static readonly DESCRIPTION = '从文件或数据加载场景';

  constructor(nodeType: string = LoadSceneNode.TYPE, name: string = LoadSceneNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('load', 'trigger', '加载');
    this.addInput('sceneId', 'string', '场景ID');
    this.addInput('sceneData', 'object', '场景数据');
    this.addInput('filePath', 'string', '文件路径');

    // 输出端口
    this.addOutput('scene', 'object', '场景对象');
    this.addOutput('sceneId', 'string', '场景ID');
    this.addOutput('metadata', 'object', '场景元数据');
    this.addOutput('onLoaded', 'trigger', '加载完成');
    this.addOutput('onError', 'trigger', '加载失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const loadTrigger = inputs?.load;
      if (!loadTrigger) {
        return this.getDefaultOutputs();
      }

      const sceneId = inputs?.sceneId as string || this.generateSceneId();
      const sceneData = inputs?.sceneData;
      const filePath = inputs?.filePath as string;

      let scene: Scene;

      if (filePath) {
        // 从文件加载（简化实现）
        scene = await globalSceneManager.loadScene(sceneId);
      } else {
        // 从数据加载
        scene = await globalSceneManager.loadScene(sceneId, sceneData);
      }

      const metadata = globalSceneManager.getSceneMetadata(sceneId);

      Debug.log('LoadSceneNode', `场景加载成功: ${sceneId}`);

      return {
        scene,
        sceneId,
        metadata,
        onLoaded: true,
        onError: false
      };

    } catch (error) {
      Debug.error('LoadSceneNode', '场景加载失败', error);
      return {
        scene: null,
        sceneId: '',
        metadata: null,
        onLoaded: false,
        onError: true
      };
    }
  }

  private generateSceneId(): string {
    return 'scene_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      scene: null,
      sceneId: '',
      metadata: null,
      onLoaded: false,
      onError: false
    };
  }
}

/**
 * 保存场景节点
 */
export class SaveSceneNode extends VisualScriptNode {
  public static readonly TYPE = 'SaveScene';
  public static readonly NAME = '保存场景';
  public static readonly DESCRIPTION = '保存场景到文件或数据';

  constructor(nodeType: string = SaveSceneNode.TYPE, name: string = SaveSceneNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('save', 'trigger', '保存');
    this.addInput('sceneId', 'string', '场景ID');
    this.addInput('filePath', 'string', '文件路径');
    this.addInput('backup', 'boolean', '创建备份');
    this.addInput('compress', 'boolean', '压缩数据');

    // 输出端口
    this.addOutput('sceneData', 'object', '场景数据');
    this.addOutput('filePath', 'string', '保存路径');
    this.addOutput('size', 'number', '文件大小');
    this.addOutput('onSaved', 'trigger', '保存完成');
    this.addOutput('onError', 'trigger', '保存失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const saveTrigger = inputs?.save;
      if (!saveTrigger) {
        return this.getDefaultOutputs();
      }

      const sceneId = inputs?.sceneId as string;
      if (!sceneId) {
        throw new Error('未提供场景ID');
      }

      const filePath = inputs?.filePath as string;
      const backup = inputs?.backup as boolean || false;
      const compress = inputs?.compress as boolean || false;

      const sceneData = await globalSceneManager.saveScene(sceneId, { backup, compress });
      const metadata = globalSceneManager.getSceneMetadata(sceneId);

      Debug.log('SaveSceneNode', `场景保存成功: ${sceneId}`);

      return {
        sceneData,
        filePath: filePath || `${sceneId}.scene`,
        size: metadata?.size || 0,
        onSaved: true,
        onError: false
      };

    } catch (error) {
      Debug.error('SaveSceneNode', '场景保存失败', error);
      return {
        sceneData: null,
        filePath: '',
        size: 0,
        onSaved: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      sceneData: null,
      filePath: '',
      size: 0,
      onSaved: false,
      onError: false
    };
  }
}

/**
 * 创建场景节点
 */
export class CreateSceneNode extends VisualScriptNode {
  public static readonly TYPE = 'CreateScene';
  public static readonly NAME = '创建场景';
  public static readonly DESCRIPTION = '创建新的空场景';

  constructor(nodeType: string = CreateSceneNode.TYPE, name: string = CreateSceneNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建');
    this.addInput('sceneId', 'string', '场景ID');
    this.addInput('sceneName', 'string', '场景名称');
    this.addInput('description', 'string', '场景描述');
    this.addInput('tags', 'array', '标签');

    // 输出端口
    this.addOutput('scene', 'object', '场景对象');
    this.addOutput('sceneId', 'string', '场景ID');
    this.addOutput('metadata', 'object', '场景元数据');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onError', 'trigger', '创建失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      if (!createTrigger) {
        return this.getDefaultOutputs();
      }

      const sceneId = inputs?.sceneId as string || this.generateSceneId();
      const sceneName = inputs?.sceneName as string || sceneId;
      const description = inputs?.description as string || '';
      const tags = inputs?.tags as string[] || [];

      const scene = globalSceneManager.createScene(sceneId);
      scene.name = sceneName;

      // 更新元数据
      const metadata = globalSceneManager.getSceneMetadata(sceneId);
      if (metadata) {
        metadata.name = sceneName;
        metadata.description = description;
        metadata.tags = tags;
      }

      Debug.log('CreateSceneNode', `场景创建成功: ${sceneId}`);

      return {
        scene,
        sceneId,
        metadata,
        onCreated: true,
        onError: false
      };

    } catch (error) {
      Debug.error('CreateSceneNode', '场景创建失败', error);
      return {
        scene: null,
        sceneId: '',
        metadata: null,
        onCreated: false,
        onError: true
      };
    }
  }

  private generateSceneId(): string {
    return 'scene_' + Math.random().toString(36).substring(2, 11);
  }

  private getDefaultOutputs(): any {
    return {
      scene: null,
      sceneId: '',
      metadata: null,
      onCreated: false,
      onError: false
    };
  }
}

/**
 * 销毁场景节点
 */
export class DestroySceneNode extends VisualScriptNode {
  public static readonly TYPE = 'DestroyScene';
  public static readonly NAME = '销毁场景';
  public static readonly DESCRIPTION = '销毁场景并清理资源';

  constructor(nodeType: string = DestroySceneNode.TYPE, name: string = DestroySceneNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('destroy', 'trigger', '销毁');
    this.addInput('sceneId', 'string', '场景ID');
    this.addInput('force', 'boolean', '强制销毁');

    // 输出端口
    this.addOutput('success', 'boolean', '销毁成功');
    this.addOutput('sceneId', 'string', '场景ID');
    this.addOutput('onDestroyed', 'trigger', '销毁完成');
    this.addOutput('onError', 'trigger', '销毁失败');
  }

  public execute(inputs?: any): any {
    try {
      const destroyTrigger = inputs?.destroy;
      if (!destroyTrigger) {
        return this.getDefaultOutputs();
      }

      const sceneId = inputs?.sceneId as string;
      if (!sceneId) {
        throw new Error('未提供场景ID');
      }

      const force = inputs?.force as boolean || false;

      // 检查是否为活动场景
      const activeScene = globalSceneManager.getActiveScene();
      if (activeScene && activeScene.name === sceneId && !force) {
        throw new Error('无法销毁活动场景，请先切换到其他场景或使用强制模式');
      }

      const success = globalSceneManager.destroyScene(sceneId);

      if (!success) {
        throw new Error('场景销毁失败');
      }

      Debug.log('DestroySceneNode', `场景销毁成功: ${sceneId}`);

      return {
        success: true,
        sceneId,
        onDestroyed: true,
        onError: false
      };

    } catch (error) {
      Debug.error('DestroySceneNode', '场景销毁失败', error);
      return {
        success: false,
        sceneId: '',
        onDestroyed: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      success: false,
      sceneId: '',
      onDestroyed: false,
      onError: false
    };
  }
}

# 批次1节点使用指南

## 概述

本文档介绍批次1.1和1.2新开发的节点的使用方法，包括渲染系统核心节点和场景管理节点。这些节点为DL引擎的视觉脚本系统提供了强大的材质管理和场景操作功能。

## 批次1.1：渲染系统核心节点

### 材质管理节点

#### 1. 创建材质节点 (CreateMaterialNode)

**功能**: 创建新的材质对象

**输入端口**:
- `create` (trigger): 创建触发器
- `materialId` (string): 材质ID（可选，自动生成）
- `materialType` (string): 材质类型（basic/standard/physical）
- `name` (string): 材质名称

**输出端口**:
- `material` (object): 创建的材质对象
- `materialId` (string): 材质ID
- `onCreated` (trigger): 创建完成事件
- `onError` (trigger): 创建失败事件

**使用示例**:
```javascript
// 创建一个标准材质
const createMaterialNode = new CreateMaterialNode();
const result = createMaterialNode.execute({
  create: true,
  materialId: 'myMaterial',
  materialType: 'standard',
  name: 'My Custom Material'
});
```

#### 2. 设置材质属性节点 (SetMaterialPropertyNode)

**功能**: 设置材质的各种属性

**输入端口**:
- `set` (trigger): 设置触发器
- `materialId` (string): 材质ID
- `property` (string): 属性名称
- `value` (any): 属性值
- `color` (object): 颜色
- `opacity` (number): 透明度
- `metalness` (number): 金属度
- `roughness` (number): 粗糙度

**输出端口**:
- `material` (object): 材质对象
- `success` (boolean): 设置成功
- `onSet` (trigger): 设置完成事件
- `onError` (trigger): 设置失败事件

**使用示例**:
```javascript
// 设置材质颜色和属性
const setPropertyNode = new SetMaterialPropertyNode();
const result = setPropertyNode.execute({
  set: true,
  materialId: 'myMaterial',
  color: new Color(0xff0000), // 红色
  opacity: 0.8,
  metalness: 0.5,
  roughness: 0.3
});
```

#### 3. 获取材质属性节点 (GetMaterialPropertyNode)

**功能**: 获取材质的各种属性值

**输入端口**:
- `get` (trigger): 获取触发器
- `materialId` (string): 材质ID
- `property` (string): 属性名称

**输出端口**:
- `value` (any): 属性值
- `color` (object): 颜色
- `opacity` (number): 透明度
- `metalness` (number): 金属度
- `roughness` (number): 粗糙度
- `onGet` (trigger): 获取完成事件
- `onError` (trigger): 获取失败事件

#### 4. 材质混合节点 (MaterialBlendNode)

**功能**: 混合两个材质创建新材质

**输入端口**:
- `blend` (trigger): 混合触发器
- `materialA` (string): 材质A ID
- `materialB` (string): 材质B ID
- `blendFactor` (number): 混合因子 (0-1)
- `blendMode` (string): 混合模式 (linear/multiply/screen/overlay)
- `outputId` (string): 输出材质ID

**输出端口**:
- `blendedMaterial` (object): 混合后的材质
- `materialId` (string): 材质ID
- `onBlended` (trigger): 混合完成事件
- `onError` (trigger): 混合失败事件

**使用示例**:
```javascript
// 混合两个材质
const blendNode = new MaterialBlendNode();
const result = blendNode.execute({
  blend: true,
  materialA: 'material1',
  materialB: 'material2',
  blendFactor: 0.5,
  blendMode: 'linear',
  outputId: 'blendedMaterial'
});
```

#### 5. 材质动画节点 (MaterialAnimationNode)

**功能**: 为材质属性创建动画效果

**输入端口**:
- `start` (trigger): 开始动画
- `stop` (trigger): 停止动画
- `materialId` (string): 材质ID
- `property` (string): 动画属性
- `fromValue` (any): 起始值
- `toValue` (any): 结束值
- `duration` (number): 持续时间（秒）
- `easing` (string): 缓动函数 (linear/easeIn/easeOut/easeInOut/bounce)
- `loop` (boolean): 循环播放

**输出端口**:
- `currentValue` (any): 当前值
- `progress` (number): 进度 (0-1)
- `isPlaying` (boolean): 正在播放
- `onStarted` (trigger): 动画开始事件
- `onCompleted` (trigger): 动画完成事件
- `onStopped` (trigger): 动画停止事件
- `onError` (trigger): 动画错误事件

**使用示例**:
```javascript
// 创建透明度动画
const animationNode = new MaterialAnimationNode();
const result = animationNode.execute({
  start: true,
  materialId: 'myMaterial',
  property: 'opacity',
  fromValue: 0.0,
  toValue: 1.0,
  duration: 2.0,
  easing: 'easeInOut',
  loop: false
});
```

## 批次1.2：场景管理节点

### 场景操作节点

#### 1. 创建场景节点 (CreateSceneNode)

**功能**: 创建新的空场景

**输入端口**:
- `create` (trigger): 创建触发器
- `sceneId` (string): 场景ID（可选，自动生成）
- `sceneName` (string): 场景名称
- `description` (string): 场景描述
- `tags` (array): 标签数组

**输出端口**:
- `scene` (object): 场景对象
- `sceneId` (string): 场景ID
- `metadata` (object): 场景元数据
- `onCreated` (trigger): 创建完成事件
- `onError` (trigger): 创建失败事件

**使用示例**:
```javascript
// 创建新场景
const createSceneNode = new CreateSceneNode();
const result = createSceneNode.execute({
  create: true,
  sceneId: 'gameLevel1',
  sceneName: '游戏关卡1',
  description: '第一个游戏关卡',
  tags: ['game', 'level', 'tutorial']
});
```

#### 2. 加载场景节点 (LoadSceneNode)

**功能**: 从文件或数据加载场景

**输入端口**:
- `load` (trigger): 加载触发器
- `sceneId` (string): 场景ID
- `sceneData` (object): 场景数据（可选）
- `filePath` (string): 文件路径（可选）

**输出端口**:
- `scene` (object): 场景对象
- `sceneId` (string): 场景ID
- `metadata` (object): 场景元数据
- `onLoaded` (trigger): 加载完成事件
- `onError` (trigger): 加载失败事件

#### 3. 保存场景节点 (SaveSceneNode)

**功能**: 保存场景到文件或数据

**输入端口**:
- `save` (trigger): 保存触发器
- `sceneId` (string): 场景ID
- `filePath` (string): 文件路径（可选）
- `backup` (boolean): 创建备份
- `compress` (boolean): 压缩数据

**输出端口**:
- `sceneData` (object): 场景数据
- `filePath` (string): 保存路径
- `size` (number): 文件大小
- `onSaved` (trigger): 保存完成事件
- `onError` (trigger): 保存失败事件

#### 4. 销毁场景节点 (DestroySceneNode)

**功能**: 销毁场景并清理资源

**输入端口**:
- `destroy` (trigger): 销毁触发器
- `sceneId` (string): 场景ID
- `force` (boolean): 强制销毁

**输出端口**:
- `success` (boolean): 销毁成功
- `sceneId` (string): 场景ID
- `onDestroyed` (trigger): 销毁完成事件
- `onError` (trigger): 销毁失败事件

## 实际应用示例

### 示例1：创建动态材质效果

```javascript
// 1. 创建材质
const createMaterial = new CreateMaterialNode();
createMaterial.execute({
  create: true,
  materialId: 'glowMaterial',
  materialType: 'standard'
});

// 2. 设置初始属性
const setProperty = new SetMaterialPropertyNode();
setProperty.execute({
  set: true,
  materialId: 'glowMaterial',
  color: new Color(0x00ff00),
  emissiveIntensity: 0.0
});

// 3. 创建发光动画
const animation = new MaterialAnimationNode();
animation.execute({
  start: true,
  materialId: 'glowMaterial',
  property: 'emissiveIntensity',
  fromValue: 0.0,
  toValue: 2.0,
  duration: 1.5,
  easing: 'easeInOut',
  loop: true
});
```

### 示例2：场景管理工作流

```javascript
// 1. 创建新场景
const createScene = new CreateSceneNode();
const sceneResult = createScene.execute({
  create: true,
  sceneId: 'mainMenu',
  sceneName: '主菜单场景',
  description: '游戏主菜单界面'
});

// 2. 保存场景
const saveScene = new SaveSceneNode();
saveScene.execute({
  save: true,
  sceneId: 'mainMenu',
  backup: true,
  compress: true
});

// 3. 加载其他场景
const loadScene = new LoadSceneNode();
loadScene.execute({
  load: true,
  sceneId: 'gameLevel1'
});
```

## 注意事项

1. **材质ID唯一性**: 确保材质ID在系统中是唯一的，避免冲突
2. **资源清理**: 使用完材质后及时清理，避免内存泄漏
3. **动画性能**: 材质动画会影响渲染性能，合理使用
4. **场景状态**: 注意场景的加载状态，避免操作未加载完成的场景
5. **错误处理**: 始终检查节点的错误输出，做好异常处理

## 下一步

批次1.1和1.2的节点为基础渲染和场景管理提供了核心功能。接下来的批次将继续开发：

- 批次1.3：资源管理节点
- 批次1.4：输入系统增强节点
- 批次1.5：物理系统增强节点
- 批次1.6：音频系统增强节点
- 批次1.7：动画系统增强节点

这些节点将进一步完善DL引擎的视觉脚本系统，实现更强大的应用开发能力。

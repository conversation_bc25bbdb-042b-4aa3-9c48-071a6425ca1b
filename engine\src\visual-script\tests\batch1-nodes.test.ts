/**
 * 批次1.1和1.2节点测试
 * 测试新开发的渲染系统核心节点和场景管理节点
 */
import { describe, test, expect, beforeEach } from '@jest/globals';
import { Color, Vector3 } from 'three';

// 导入批次1.1节点
import {
  CreateMaterialNode,
  SetMaterialPropertyNode,
  GetMaterialPropertyNode,
  MaterialBlendNode,
  MaterialAnimationNode
} from '../nodes/rendering/RenderingNodes';

// 导入批次1.2节点
import {
  LoadSceneNode,
  SaveSceneNode,
  CreateSceneNode,
  DestroySceneNode
} from '../nodes/scene/SceneManagementNodes';

describe('批次1.1：渲染系统核心节点测试', () => {
  describe('CreateMaterialNode', () => {
    let node: CreateMaterialNode;

    beforeEach(() => {
      node = new CreateMaterialNode();
    });

    test('应该能够创建材质节点', () => {
      expect(node).toBeDefined();
      expect(node.getType()).toBe('CreateMaterial');
      expect(node.getName()).toBe('创建材质');
    });

    test('应该能够创建基础材质', () => {
      const inputs = {
        create: true,
        materialId: 'test_material',
        materialType: 'standard',
        name: 'Test Material'
      };

      const result = node.execute(inputs);

      expect(result.onCreated).toBe(true);
      expect(result.onError).toBe(false);
      expect(result.materialId).toBe('test_material');
      expect(result.material).toBeDefined();
    });

    test('应该能够生成随机材质ID', () => {
      const inputs = {
        create: true,
        materialType: 'basic'
      };

      const result = node.execute(inputs);

      expect(result.onCreated).toBe(true);
      expect(result.materialId).toMatch(/^mat_[a-z0-9]+$/);
    });

    test('没有触发器时应该返回默认输出', () => {
      const result = node.execute({});

      expect(result.onCreated).toBe(false);
      expect(result.onError).toBe(false);
      expect(result.material).toBeNull();
    });
  });

  describe('SetMaterialPropertyNode', () => {
    let node: SetMaterialPropertyNode;
    let createNode: CreateMaterialNode;

    beforeEach(() => {
      node = new SetMaterialPropertyNode();
      createNode = new CreateMaterialNode();
    });

    test('应该能够设置材质属性', () => {
      // 先创建材质
      const createResult = createNode.execute({
        create: true,
        materialId: 'test_material'
      });

      expect(createResult.onCreated).toBe(true);

      // 设置属性
      const inputs = {
        set: true,
        materialId: 'test_material',
        color: new Color(0xff0000),
        opacity: 0.5,
        metalness: 0.8,
        roughness: 0.2
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.onSet).toBe(true);
      expect(result.onError).toBe(false);
    });

    test('材质不存在时应该返回错误', () => {
      const inputs = {
        set: true,
        materialId: 'nonexistent_material',
        color: new Color(0xff0000)
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(false);
      expect(result.onError).toBe(true);
    });

    test('未提供材质ID时应该返回错误', () => {
      const inputs = {
        set: true,
        color: new Color(0xff0000)
      };

      const result = node.execute(inputs);

      expect(result.success).toBe(false);
      expect(result.onError).toBe(true);
    });
  });

  describe('GetMaterialPropertyNode', () => {
    let node: GetMaterialPropertyNode;
    let createNode: CreateMaterialNode;

    beforeEach(() => {
      node = new GetMaterialPropertyNode();
      createNode = new CreateMaterialNode();
    });

    test('应该能够获取材质属性', () => {
      // 先创建材质
      const createResult = createNode.execute({
        create: true,
        materialId: 'test_material'
      });

      expect(createResult.onCreated).toBe(true);

      // 获取属性
      const inputs = {
        get: true,
        materialId: 'test_material',
        property: 'opacity'
      };

      const result = node.execute(inputs);

      expect(result.onGet).toBe(true);
      expect(result.onError).toBe(false);
      expect(result.value).toBeDefined();
    });

    test('材质不存在时应该返回错误', () => {
      const inputs = {
        get: true,
        materialId: 'nonexistent_material',
        property: 'color'
      };

      const result = node.execute(inputs);

      expect(result.onError).toBe(true);
    });
  });

  describe('MaterialBlendNode', () => {
    let node: MaterialBlendNode;
    let createNode: CreateMaterialNode;

    beforeEach(() => {
      node = new MaterialBlendNode();
      createNode = new CreateMaterialNode();
    });

    test('应该能够混合两个材质', () => {
      // 创建两个材质
      createNode.execute({
        create: true,
        materialId: 'material_a'
      });

      createNode.execute({
        create: true,
        materialId: 'material_b'
      });

      // 混合材质
      const inputs = {
        blend: true,
        materialA: 'material_a',
        materialB: 'material_b',
        blendFactor: 0.5,
        blendMode: 'linear',
        outputId: 'blended_material'
      };

      const result = node.execute(inputs);

      expect(result.onBlended).toBe(true);
      expect(result.onError).toBe(false);
      expect(result.materialId).toBe('blended_material');
      expect(result.blendedMaterial).toBeDefined();
    });

    test('缺少材质时应该返回错误', () => {
      const inputs = {
        blend: true,
        materialA: 'nonexistent_a',
        materialB: 'nonexistent_b'
      };

      const result = node.execute(inputs);

      expect(result.onError).toBe(true);
    });
  });

  describe('MaterialAnimationNode', () => {
    let node: MaterialAnimationNode;
    let createNode: CreateMaterialNode;

    beforeEach(() => {
      node = new MaterialAnimationNode();
      createNode = new CreateMaterialNode();
    });

    test('应该能够开始材质动画', () => {
      // 创建材质
      createNode.execute({
        create: true,
        materialId: 'animated_material'
      });

      // 开始动画
      const inputs = {
        start: true,
        materialId: 'animated_material',
        property: 'opacity',
        fromValue: 0.0,
        toValue: 1.0,
        duration: 1.0,
        easing: 'linear'
      };

      const result = node.execute(inputs);

      expect(result.onStarted).toBe(true);
      expect(result.onError).toBe(false);
      expect(result.isPlaying).toBe(true);
      expect(result.progress).toBe(0);
    });

    test('应该能够停止材质动画', () => {
      const inputs = {
        stop: true
      };

      const result = node.execute(inputs);

      expect(result.onStopped).toBe(true);
      expect(result.isPlaying).toBe(false);
    });

    test('缺少参数时应该返回错误', () => {
      const inputs = {
        start: true,
        materialId: 'test_material'
        // 缺少其他必要参数
      };

      const result = node.execute(inputs);

      expect(result.onError).toBe(true);
    });
  });
});

describe('批次1.2：场景管理节点测试', () => {
  describe('CreateSceneNode', () => {
    let node: CreateSceneNode;

    beforeEach(() => {
      node = new CreateSceneNode();
    });

    test('应该能够创建场景节点', () => {
      expect(node).toBeDefined();
      expect(node.getType()).toBe('CreateScene');
      expect(node.getName()).toBe('创建场景');
    });

    test('应该能够创建新场景', () => {
      const inputs = {
        create: true,
        sceneId: 'test_scene',
        sceneName: 'Test Scene',
        description: 'A test scene',
        tags: ['test', 'demo']
      };

      const result = node.execute(inputs);

      expect(result.onCreated).toBe(true);
      expect(result.onError).toBe(false);
      expect(result.sceneId).toBe('test_scene');
      expect(result.scene).toBeDefined();
      expect(result.metadata).toBeDefined();
    });

    test('应该能够生成随机场景ID', () => {
      const inputs = {
        create: true,
        sceneName: 'Auto Scene'
      };

      const result = node.execute(inputs);

      expect(result.onCreated).toBe(true);
      expect(result.sceneId).toMatch(/^scene_[a-z0-9]+$/);
    });
  });

  describe('LoadSceneNode', () => {
    let node: LoadSceneNode;

    beforeEach(() => {
      node = new LoadSceneNode();
    });

    test('应该能够加载场景', async () => {
      const inputs = {
        load: true,
        sceneId: 'loaded_scene'
      };

      const result = await node.execute(inputs);

      expect(result.onLoaded).toBe(true);
      expect(result.onError).toBe(false);
      expect(result.sceneId).toBe('loaded_scene');
      expect(result.scene).toBeDefined();
    });

    test('应该能够从数据加载场景', async () => {
      const sceneData = {
        name: 'Data Scene',
        objects: []
      };

      const inputs = {
        load: true,
        sceneId: 'data_scene',
        sceneData
      };

      const result = await node.execute(inputs);

      expect(result.onLoaded).toBe(true);
      expect(result.scene).toBeDefined();
    });
  });

  describe('SaveSceneNode', () => {
    let saveNode: SaveSceneNode;
    let createNode: CreateSceneNode;

    beforeEach(() => {
      saveNode = new SaveSceneNode();
      createNode = new CreateSceneNode();
    });

    test('应该能够保存场景', async () => {
      // 先创建场景
      const createResult = createNode.execute({
        create: true,
        sceneId: 'save_test_scene'
      });

      expect(createResult.onCreated).toBe(true);

      // 保存场景
      const inputs = {
        save: true,
        sceneId: 'save_test_scene',
        backup: true,
        compress: false
      };

      const result = await saveNode.execute(inputs);

      expect(result.onSaved).toBe(true);
      expect(result.onError).toBe(false);
      expect(result.sceneData).toBeDefined();
    });

    test('场景不存在时应该返回错误', async () => {
      const inputs = {
        save: true,
        sceneId: 'nonexistent_scene'
      };

      const result = await saveNode.execute(inputs);

      expect(result.onError).toBe(true);
    });
  });

  describe('DestroySceneNode', () => {
    let destroyNode: DestroySceneNode;
    let createNode: CreateSceneNode;

    beforeEach(() => {
      destroyNode = new DestroySceneNode();
      createNode = new CreateSceneNode();
    });

    test('应该能够销毁场景', () => {
      // 先创建场景
      const createResult = createNode.execute({
        create: true,
        sceneId: 'destroy_test_scene'
      });

      expect(createResult.onCreated).toBe(true);

      // 销毁场景
      const inputs = {
        destroy: true,
        sceneId: 'destroy_test_scene'
      };

      const result = destroyNode.execute(inputs);

      expect(result.success).toBe(true);
      expect(result.onDestroyed).toBe(true);
      expect(result.onError).toBe(false);
    });

    test('场景不存在时应该返回错误', () => {
      const inputs = {
        destroy: true,
        sceneId: 'nonexistent_scene'
      };

      const result = destroyNode.execute(inputs);

      expect(result.onError).toBe(true);
    });
  });
});
